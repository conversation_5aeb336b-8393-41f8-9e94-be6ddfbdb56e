# Deployment Guide

Comprehensive deployment guide for the OpenWebUI RAG Code Server, covering Docker deployment, production setup, configuration, and monitoring.

## 🎯 Deployment Overview

The OpenWebUI RAG Code Server supports multiple deployment scenarios:

1. **Development**: Local development with hot-reload
2. **Testing**: Isolated testing environment
3. **Staging**: Pre-production validation
4. **Production**: High-availability production deployment

## 🐳 Docker Deployment

### Quick Start Deployment

```bash
# Clone repository
git clone <repository-url>
cd openwebui_rag_code_server

# Create environment file
cp .env.example .env

# Deploy with Docker Compose
docker-compose up --build -d

# Verify deployment
curl http://localhost:5002/health
```

### Production Docker Deployment

#### 1. Environment Configuration

Create production environment file:

```bash
# .env.production
# Core Configuration
ENVIRONMENT=production
LOG_LEVEL=INFO
DEBUG=false

# Server Configuration
CODE_ANALYZER_PORT=5002
WEB_MANAGEMENT_PORT=5003
HOST=0.0.0.0

# External Services
OLLAMA_HOST=http://ollama:11434
OLLAMA_MODEL=llama3:latest

# Database Configuration
CHROMA_DB_BASE_PATH=/app/data/chroma_db
SOURCE_CODE_BASE_PATH=/app/data/source_code

# Security
API_KEY_REQUIRED=true
API_KEY=your_secure_api_key_here
CORS_ORIGINS=["https://your-domain.com"]

# Performance
MAX_WORKERS=4
WORKER_TIMEOUT=300
CHUNK_SIZE=1000
EMBEDDING_BATCH_SIZE=100

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30
```

#### 2. Production Docker Compose

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  code-analyzer-server:
    build:
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "5002:5002"
      - "9090:9090"  # Metrics port
    environment:
      - ENVIRONMENT=production
    env_file:
      - .env.production
    volumes:
      - ./data/source_code:/app/data/source_code:ro
      - chroma_data:/app/data/chroma_db
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'

  web-management:
    build:
      context: .
      dockerfile: Dockerfile.web
    ports:
      - "5003:5003"
    environment:
      - CODE_ANALYZER_BASE_URL=http://code-analyzer-server:5002
    depends_on:
      code-analyzer-server:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5003/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - code-analyzer-server
      - web-management
    restart: unless-stopped

volumes:
  chroma_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/code-analyzer/data/chroma_db

networks:
  default:
    name: code-analyzer-network
```

#### 3. Production Dockerfile

```dockerfile
# Dockerfile.prod
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN useradd --create-home --shell /bin/bash app

# Set working directory
WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Set ownership
RUN chown -R app:app /app

# Switch to app user
USER app

# Create necessary directories
RUN mkdir -p /app/logs /app/data/chroma_db /app/data/source_code

# Expose ports
EXPOSE 5002 9090

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:5002/health || exit 1

# Start application
CMD ["python", "main.py"]
```

### Nginx Configuration

```nginx
# nginx/nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream code_analyzer {
        server code-analyzer-server:5002;
    }

    upstream web_management {
        server web-management:5003;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=web:10m rate=5r/s;

    server {
        listen 80;
        server_name your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        # API endpoints
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://code_analyzer;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_timeout 300s;
        }

        # Web management interface
        location /management/ {
            limit_req zone=web burst=10 nodelay;
            proxy_pass http://web_management/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health checks
        location /health {
            proxy_pass http://code_analyzer/health;
            access_log off;
        }

        # Metrics (restrict access)
        location /metrics {
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
            proxy_pass http://code_analyzer:9090/metrics;
        }
    }
}
```

## ☸️ Kubernetes Deployment

### Kubernetes Manifests

#### 1. Namespace and ConfigMap

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: code-analyzer

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: code-analyzer-config
  namespace: code-analyzer
data:
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  CODE_ANALYZER_PORT: "5002"
  WEB_MANAGEMENT_PORT: "5003"
  OLLAMA_HOST: "http://ollama-service:11434"
  MAX_WORKERS: "4"
  CHUNK_SIZE: "1000"
```

#### 2. Secrets

```yaml
# k8s/secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: code-analyzer-secrets
  namespace: code-analyzer
type: Opaque
data:
  API_KEY: <base64-encoded-api-key>
  OPENWEBUI_API_KEY: <base64-encoded-openwebui-key>
```

#### 3. Persistent Volumes

```yaml
# k8s/pv.yaml
apiVersion: v1
kind: PersistentVolume
metadata:
  name: chroma-data-pv
spec:
  capacity:
    storage: 100Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: fast-ssd
  hostPath:
    path: /opt/code-analyzer/data

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: chroma-data-pvc
  namespace: code-analyzer
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
  storageClassName: fast-ssd
```

#### 4. Deployment

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: code-analyzer-server
  namespace: code-analyzer
spec:
  replicas: 3
  selector:
    matchLabels:
      app: code-analyzer-server
  template:
    metadata:
      labels:
        app: code-analyzer-server
    spec:
      containers:
      - name: code-analyzer
        image: code-analyzer:latest
        ports:
        - containerPort: 5002
        - containerPort: 9090
        envFrom:
        - configMapRef:
            name: code-analyzer-config
        - secretRef:
            name: code-analyzer-secrets
        volumeMounts:
        - name: chroma-data
          mountPath: /app/data/chroma_db
        - name: source-code
          mountPath: /app/data/source_code
          readOnly: true
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 5002
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 5002
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
      - name: chroma-data
        persistentVolumeClaim:
          claimName: chroma-data-pvc
      - name: source-code
        hostPath:
          path: /opt/source-code
```

#### 5. Service and Ingress

```yaml
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: code-analyzer-service
  namespace: code-analyzer
spec:
  selector:
    app: code-analyzer-server
  ports:
  - name: api
    port: 5002
    targetPort: 5002
  - name: metrics
    port: 9090
    targetPort: 9090

---
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: code-analyzer-ingress
  namespace: code-analyzer
  annotations:
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api.your-domain.com
    secretName: code-analyzer-tls
  rules:
  - host: api.your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: code-analyzer-service
            port:
              number: 5002
```

## 🔧 Configuration Management

### Environment-Specific Configuration

#### Development
```bash
# .env.development
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG
RELOAD=true
OLLAMA_HOST=http://localhost:11434
```

#### Testing
```bash
# .env.testing
ENVIRONMENT=testing
DEBUG=false
LOG_LEVEL=INFO
CHROMA_DB_BASE_PATH=./test_data/chroma_db
SOURCE_CODE_BASE_PATH=./test_data/source_code
```

#### Production
```bash
# .env.production
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=WARNING
API_KEY_REQUIRED=true
ENABLE_METRICS=true
CORS_ORIGINS=["https://your-domain.com"]
```

### Configuration Validation

```python
# config/validation.py
from pydantic import BaseSettings, validator

class Settings(BaseSettings):
    environment: str = "development"
    debug: bool = False
    log_level: str = "INFO"
    
    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Invalid log level: {v}')
        return v.upper()
    
    class Config:
        env_file = ".env"
```

## 📊 Monitoring and Observability

### Prometheus Metrics

```python
# monitoring/metrics.py
from prometheus_client import Counter, Histogram, Gauge

# Request metrics
REQUEST_COUNT = Counter('requests_total', 'Total requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('request_duration_seconds', 'Request duration')

# Application metrics
ACTIVE_CODEBASES = Gauge('active_codebases_total', 'Number of active codebases')
PROCESSING_QUEUE = Gauge('processing_queue_size', 'Size of processing queue')
```

### Grafana Dashboard

```json
{
  "dashboard": {
    "title": "Code Analyzer Metrics",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, request_duration_seconds_bucket)",
            "legendFormat": "95th percentile"
          }
        ]
      }
    ]
  }
}
```

### Log Aggregation

```yaml
# logging/fluentd.conf
<source>
  @type tail
  path /app/logs/*.log
  pos_file /var/log/fluentd/code-analyzer.log.pos
  tag code-analyzer.*
  format json
</source>

<match code-analyzer.**>
  @type elasticsearch
  host elasticsearch
  port 9200
  index_name code-analyzer
  type_name _doc
</match>
```

## 🔒 Security Configuration

### SSL/TLS Setup

```bash
# Generate SSL certificates
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout nginx/ssl/key.pem \
  -out nginx/ssl/cert.pem \
  -subj "/C=US/ST=State/L=City/O=Organization/CN=your-domain.com"

# Or use Let's Encrypt
certbot certonly --standalone -d your-domain.com
```

### API Security

```python
# security/auth.py
from fastapi import HTTPException, Depends
from fastapi.security import HTTPBearer

security = HTTPBearer()

async def verify_api_key(token: str = Depends(security)):
    if token.credentials != os.getenv("API_KEY"):
        raise HTTPException(status_code=401, detail="Invalid API key")
    return token
```

### Network Security

```bash
# Firewall configuration
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw deny 5002/tcp   # Block direct API access
ufw deny 5003/tcp   # Block direct web access
ufw enable
```

## 🚀 Deployment Automation

### CI/CD Pipeline

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Build Docker image
      run: docker build -t code-analyzer:${{ github.sha }} .
    
    - name: Push to registry
      run: |
        docker tag code-analyzer:${{ github.sha }} registry.com/code-analyzer:latest
        docker push registry.com/code-analyzer:latest
    
    - name: Deploy to production
      run: |
        kubectl set image deployment/code-analyzer-server \
          code-analyzer=registry.com/code-analyzer:${{ github.sha }}
```

### Health Checks and Rollback

```bash
#!/bin/bash
# scripts/deploy.sh

# Deploy new version
kubectl apply -f k8s/

# Wait for rollout
kubectl rollout status deployment/code-analyzer-server -n code-analyzer

# Health check
if ! curl -f http://api.your-domain.com/health; then
  echo "Health check failed, rolling back..."
  kubectl rollout undo deployment/code-analyzer-server -n code-analyzer
  exit 1
fi

echo "Deployment successful!"
```

## 📈 Performance Optimization

### Resource Allocation

```yaml
# Production resource limits
resources:
  requests:
    memory: "2Gi"
    cpu: "1000m"
  limits:
    memory: "4Gi"
    cpu: "2000m"
```

### Caching Strategy

```python
# caching/redis.py
import redis
from functools import wraps

redis_client = redis.Redis(host='redis', port=6379, db=0)

def cache_result(expiration=3600):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            cached = redis_client.get(cache_key)
            if cached:
                return json.loads(cached)
            
            result = await func(*args, **kwargs)
            redis_client.setex(cache_key, expiration, json.dumps(result))
            return result
        return wrapper
    return decorator
```

This deployment guide provides comprehensive coverage of production deployment scenarios, from simple Docker deployments to enterprise Kubernetes clusters with full monitoring and security configurations.
