#!/usr/bin/env python3
"""
Unit tests for web_interface_integration.py
Tests the Flask-based hybrid code analyzer web interface
"""

import pytest
import httpx
import asyncio
import json
from typing import Dict, Any


class TestWebInterfaceIntegration:
    """Test web interface integration functionality."""

    # Note: This assumes web_interface_integration.py is running on a specific port
    # You may need to adjust the port based on your configuration
    WEB_INTERFACE_URL = "http://localhost:5004"  # Assuming it runs on port 5004
    
    @pytest.fixture
    async def web_interface_client(self):
        """Create an HTTP client for web interface integration testing."""
        async with httpx.AsyncClient(
            base_url=self.WEB_INTERFACE_URL,
            timeout=30.0
        ) as client:
            yield client

    @pytest.mark.asyncio
    async def test_web_interface_accessibility(self, web_interface_client: httpx.AsyncClient):
        """Test that web interface integration server is accessible."""
        try:
            response = await web_interface_client.get("/")
            assert response.status_code == 200
            print("✅ Web interface integration server accessible")
        except httpx.ConnectError:
            pytest.skip("Web interface integration server not accessible on port 5004")
        except Exception as e:
            pytest.fail(f"Unexpected error connecting to web interface server: {e}")

    @pytest.mark.asyncio
    async def test_main_interface_endpoint(self, web_interface_client: httpx.AsyncClient):
        """Test the main web interface endpoint."""
        response = await web_interface_client.get("/")
        assert response.status_code == 200
        assert "text/html" in response.headers.get("content-type", "")
        
        # Check that the HTML contains expected elements
        html_content = response.text
        assert "Hybrid Code Analyzer" in html_content
        assert "Model Selection" in html_content
        print("✅ Main interface endpoint returns proper HTML")

    @pytest.mark.asyncio
    async def test_models_endpoint(self, web_interface_client: httpx.AsyncClient):
        """Test the models endpoint."""
        response = await web_interface_client.get("/models")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, dict)
        
        # Should contain model information
        expected_fields = ["local_models", "remote_models"]
        for field in expected_fields:
            if field in data:
                assert isinstance(data[field], list)
                print(f"✅ Models endpoint contains {field}: {len(data[field])} models")

    @pytest.mark.asyncio
    async def test_benchmark_endpoint(self, web_interface_client: httpx.AsyncClient):
        """Test the benchmark endpoint."""
        response = await web_interface_client.get("/benchmark")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, dict)
        print("✅ Benchmark endpoint accessible")

    @pytest.mark.asyncio
    async def test_analyze_endpoint_basic(self, web_interface_client: httpx.AsyncClient):
        """Test the analyze endpoint with basic query."""
        payload = {
            "query": "test query",
            "model_type": "local",
            "model_name": "test_model"
        }
        
        response = await web_interface_client.post("/analyze", json=payload)
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, dict)
        assert "success" in data
        print("✅ Analyze endpoint handles basic requests")

    @pytest.mark.asyncio
    async def test_analyze_endpoint_with_different_models(self, web_interface_client: httpx.AsyncClient):
        """Test the analyze endpoint with different model configurations."""
        test_cases = [
            {
                "query": "memory management",
                "model_type": "local",
                "model_name": "llama3"
            },
            {
                "query": "error handling patterns",
                "model_type": "remote",
                "model_name": "llama3:latest"
            }
        ]
        
        for test_case in test_cases:
            response = await web_interface_client.post("/analyze", json=test_case)
            assert response.status_code == 200
            
            data = response.json()
            assert isinstance(data, dict)
            print(f"✅ Analyze endpoint handles {test_case['model_type']} model requests")

    @pytest.mark.asyncio
    async def test_analyze_endpoint_error_handling(self, web_interface_client: httpx.AsyncClient):
        """Test error handling in the analyze endpoint."""
        # Test with missing required fields
        payload = {"query": "test"}  # Missing model information
        
        response = await web_interface_client.post("/analyze", json=payload)
        assert response.status_code == 200  # Flask typically returns 200 even for errors
        
        data = response.json()
        assert isinstance(data, dict)
        # Should handle missing fields gracefully
        print("✅ Analyze endpoint handles missing fields gracefully")

    @pytest.mark.asyncio
    async def test_analyze_endpoint_empty_query(self, web_interface_client: httpx.AsyncClient):
        """Test analyze endpoint with empty query."""
        payload = {
            "query": "",
            "model_type": "local",
            "model_name": "test_model"
        }
        
        response = await web_interface_client.post("/analyze", json=payload)
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, dict)
        print("✅ Analyze endpoint handles empty queries")

    @pytest.mark.asyncio
    async def test_analyze_endpoint_invalid_json(self, web_interface_client: httpx.AsyncClient):
        """Test analyze endpoint with invalid JSON."""
        response = await web_interface_client.post("/analyze", content="invalid json")
        # Should handle invalid JSON gracefully
        assert response.status_code in [400, 500]
        print("✅ Analyze endpoint handles invalid JSON")

    @pytest.mark.asyncio
    async def test_html_template_structure(self, web_interface_client: httpx.AsyncClient):
        """Test that the HTML template contains expected structure."""
        response = await web_interface_client.get("/")
        assert response.status_code == 200
        
        html_content = response.text
        
        # Check for key HTML elements
        expected_elements = [
            "model-selector",
            "query-section",
            "submit-btn",
            "results",
            "performance-info"
        ]
        
        for element in expected_elements:
            assert element in html_content
            print(f"✅ HTML contains {element} element")

    @pytest.mark.asyncio
    async def test_css_styling_present(self, web_interface_client: httpx.AsyncClient):
        """Test that CSS styling is present in the HTML."""
        response = await web_interface_client.get("/")
        assert response.status_code == 200
        
        html_content = response.text
        
        # Check for CSS styles
        assert "<style>" in html_content
        assert "font-family" in html_content
        assert "background" in html_content
        print("✅ HTML contains CSS styling")

    @pytest.mark.asyncio
    async def test_javascript_functionality(self, web_interface_client: httpx.AsyncClient):
        """Test that JavaScript functionality is present."""
        response = await web_interface_client.get("/")
        assert response.status_code == 200
        
        html_content = response.text
        
        # Check for JavaScript
        assert "<script>" in html_content
        assert "fetch" in html_content or "XMLHttpRequest" in html_content
        print("✅ HTML contains JavaScript functionality")

    @pytest.mark.asyncio
    async def test_model_selection_interface(self, web_interface_client: httpx.AsyncClient):
        """Test that model selection interface elements are present."""
        response = await web_interface_client.get("/")
        assert response.status_code == 200
        
        html_content = response.text
        
        # Check for model selection elements
        expected_model_elements = [
            "Local Models",
            "Remote Models",
            "model-option",
            "radio"
        ]
        
        for element in expected_model_elements:
            if element in html_content:
                print(f"✅ HTML contains {element} for model selection")

    @pytest.mark.asyncio
    async def test_response_format_consistency(self, web_interface_client: httpx.AsyncClient):
        """Test that API responses follow consistent JSON format."""
        endpoints_to_test = [
            ("/models", "GET"),
            ("/benchmark", "GET")
        ]
        
        for endpoint, method in endpoints_to_test:
            if method == "GET":
                response = await web_interface_client.get(endpoint)
            else:
                response = await web_interface_client.post(endpoint, json={})
            
            assert response.status_code == 200
            
            data = response.json()
            assert isinstance(data, dict)
            print(f"✅ {endpoint} returns consistent JSON format")

    @pytest.mark.asyncio
    async def test_hybrid_analyzer_integration(self, web_interface_client: httpx.AsyncClient):
        """Test integration with HybridCodeAnalyzer."""
        # Test that the interface can get models
        response = await web_interface_client.get("/models")
        assert response.status_code == 200
        
        models_data = response.json()
        
        # Test that analyze endpoint works with model data
        if "local_models" in models_data and models_data["local_models"]:
            first_local_model = models_data["local_models"][0]
            
            payload = {
                "query": "test integration",
                "model_type": "local",
                "model_name": first_local_model
            }
            
            response = await web_interface_client.post("/analyze", json=payload)
            assert response.status_code == 200
            print("✅ Hybrid analyzer integration working")

    @pytest.mark.asyncio
    async def test_performance_response_times(self, web_interface_client: httpx.AsyncClient):
        """Test that endpoints respond within reasonable time."""
        import time
        
        endpoints_to_test = [
            ("/", "GET"),
            ("/models", "GET"),
            ("/benchmark", "GET")
        ]
        
        for endpoint, method in endpoints_to_test:
            start_time = time.time()
            
            if method == "GET":
                response = await web_interface_client.get(endpoint)
            else:
                response = await web_interface_client.post(endpoint, json={})
            
            end_time = time.time()
            response_time = end_time - start_time
            
            assert response.status_code == 200
            assert response_time < 10.0  # Should respond within 10 seconds
            print(f"✅ {endpoint} responded in {response_time:.2f}s")

    @pytest.mark.asyncio
    async def test_concurrent_requests(self, web_interface_client: httpx.AsyncClient):
        """Test handling of concurrent requests."""
        # Create multiple concurrent requests
        tasks = []
        for i in range(5):
            task = web_interface_client.get("/models")
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Check that all requests succeeded
        successful_responses = 0
        for response in responses:
            if isinstance(response, httpx.Response) and response.status_code == 200:
                successful_responses += 1
        
        assert successful_responses >= 3  # At least 3 out of 5 should succeed
        print(f"✅ Handled {successful_responses}/5 concurrent requests successfully")


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
