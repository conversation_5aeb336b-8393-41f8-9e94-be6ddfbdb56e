# Installation Guide

Complete installation guide for the OpenWebUI RAG Code Server, covering Docker deployment, local development setup, and OpenWebUI tool integration.

## 📋 Prerequisites

### System Requirements
- **Operating System**: Linux, macOS, or Windows with WSL2
- **Docker**: Docker Engine 20.10+ and Docker Compose 2.0+
- **Python**: 3.8+ (for local development)
- **Memory**: 8GB RAM minimum, 16GB recommended
- **Storage**: 10GB free space minimum

### External Services
- **Ollama Service**: For AI-powered code analysis
- **OpenWebUI**: For tool integration (optional)

## 🚀 Quick Installation (Docker)

### 1. Clone Repository
```bash
git clone <repository-url>
cd openwebui_rag_code_server
```

### 2. Prepare Source Code Directory
```bash
# Create source code directory
mkdir -p source_code

# Copy your codebases to analyze
# Example structure:
# source_code/
# ├── my_cpp_project/
# ├── my_python_app/
# ├── my_csharp_service/
# └── my_web_frontend/
```

### 3. Configure Environment
```bash
# Copy example environment file
cp .env.example .env

# Edit configuration (optional)
nano .env
```

### 4. Deploy with Docker Compose
```bash
# Build and start all services
docker-compose up --build -d

# Verify deployment
curl http://localhost:5002/health
```

### 5. Access Services
- **Code Analysis API**: http://localhost:5002
- **Web Management Dashboard**: http://localhost:5003
- **API Documentation**: http://localhost:5002/docs

## 🔧 Detailed Installation

### Docker Compose Configuration

The project includes a complete `docker-compose.yml` with the following services:

```yaml
services:
  code-analyzer-server:
    ports:
      - "5002:5002"
    volumes:
      - ./source_code:/app/source_code:ro
      - chroma_db:/app/chroma_db
    environment:
      - OLLAMA_HOST=http://ollama:11434
  
  web-management:
    ports:
      - "5003:5003"
    depends_on:
      - code-analyzer-server
```

### Environment Variables

Create a `.env` file with the following configuration:

```bash
# Core Configuration
OLLAMA_HOST=http://ollama:11434
CHROMA_DB_BASE_PATH=./chroma_db
SOURCE_CODE_BASE_PATH=./source_code

# Server Configuration
CODE_ANALYZER_PORT=5002
WEB_MANAGEMENT_PORT=5003

# OpenWebUI Integration
OPENWEBUI_API_KEY=your_api_key_here
OPENWEBUI_BASE_URL=http://localhost:8080

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/code_analyzer.log

# Performance
MAX_WORKERS=4
CHUNK_SIZE=1000
EMBEDDING_BATCH_SIZE=100
```

## 🐳 Docker Deployment Options

### Option 1: Full Stack Deployment
```bash
# Deploy everything including Ollama
docker-compose -f docker-compose.full.yml up -d
```

### Option 2: Code Analyzer Only
```bash
# Deploy only the code analyzer (external Ollama)
docker-compose up code-analyzer-server -d
```

### Option 3: Development Mode
```bash
# Deploy with development overrides
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
```

## 💻 Local Development Setup

### 1. Python Environment
```bash
# Create virtual environment
python -m venv venv

# Activate environment
# Linux/macOS:
source venv/bin/activate
# Windows:
venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Install Tree-sitter Languages
```bash
# Install tree-sitter language pack
pip install tree-sitter-language-pack

# Or install specific languages
pip install tree-sitter-python tree-sitter-cpp tree-sitter-c-sharp
```

### 3. Setup ChromaDB
```bash
# Create database directory
mkdir -p chroma_db

# Initialize database (optional - auto-created)
python -c "import chromadb; chromadb.PersistentClient(path='./chroma_db')"
```

### 4. Configure Ollama Connection
```bash
# Test Ollama connection
curl http://localhost:11434/api/tags

# Or configure remote Ollama
export OLLAMA_HOST=http://your-ollama-server:11434
```

### 5. Run Development Server
```bash
# Start the main server
python main.py

# Or with auto-reload
uvicorn main:app --reload --host 0.0.0.0 --port 5002
```

### 6. Run Web Management Interface
```bash
# In a separate terminal
python web_management_server.py

# Or with uvicorn
uvicorn web_management_server:app --reload --host 0.0.0.0 --port 5003
```

## 🛠️ OpenWebUI Tool Installation

### 1. Access OpenWebUI Interface
```bash
# Open your OpenWebUI instance
http://your-openwebui-server:8080
```

### 2. Install the Tool
1. Navigate to **Workspace** → **Tools**
2. Click **"+"** or **"Add Tool"** button
3. Choose **"Import from File"** or **"Manual Installation"**
4. Copy the contents of `open_webui_code_analyzer_tool.py`
5. Paste into the tool editor
6. Click **"Save"** or **"Install"**

### 3. Configure Tool Settings
```python
# Update the valves in the tool configuration
class Valves:
    CODE_ANALYZER_BASE_URL: str = "http://your-server:5002"
    OPENWEBUI_API_KEY: str = "your_api_key"
    DEFAULT_MAX_RESULTS: int = 10
    ENABLE_DEBUG_MODE: bool = False
```

### 4. Enable Tool for Models
1. Go to **Workspace** → **Models**
2. Find your target model (e.g., **llama3:latest**)
3. Click the **edit** (✏️) icon
4. Scroll to **"Tools"** section
5. Check the box for **"Code Analysis Tool"**
6. Click **"Save"**

### 5. Test Tool Installation
```bash
# In OpenWebUI chat, try:
"list codebases"
"select codebase my_project"
"search code memory allocation"
```

## 🔍 Verification Steps

### 1. Health Check
```bash
# Check main server
curl http://localhost:5002/health

# Expected response:
{
  "status": "healthy",
  "version": "3.0.0",
  "supported_languages": 27,
  "services": {
    "ollama": "online",
    "chromadb": "online",
    "source_directory": "available"
  }
}
```

### 2. Web Interface Check
```bash
# Check web management interface
curl http://localhost:5003/api/health

# Open in browser
http://localhost:5003
```

### 3. API Functionality Test
```bash
# List available codebases
curl -X POST http://localhost:5002/tools/list_codebases \
  -H "Content-Type: application/json" \
  -d '{}'

# Process a codebase
curl -X POST http://localhost:5002/tools/process_codebase \
  -H "Content-Type: application/json" \
  -d '{"codebase_name": "test_project"}'
```

### 4. OpenWebUI Integration Test
1. Open OpenWebUI chat interface
2. Ensure the Code Analysis Tool is enabled
3. Try basic commands:
   - `"status"` - Should show server status
   - `"list codebases"` - Should show available codebases
   - `"help"` - Should show available commands

## 🚨 Troubleshooting Installation

### Common Issues

#### Docker Issues
```bash
# Permission denied
sudo chown -R $USER:$USER ./chroma_db ./source_code

# Port conflicts
docker-compose down
sudo netstat -tulpn | grep :5002
```

#### Python Dependencies
```bash
# Upgrade pip
pip install --upgrade pip

# Install with specific versions
pip install -r requirements.txt --force-reinstall
```

#### Ollama Connection
```bash
# Test Ollama connectivity
curl http://localhost:11434/api/tags

# Check Docker network
docker network ls
docker network inspect openwebui_rag_code_server_default
```

#### ChromaDB Issues
```bash
# Reset database
rm -rf chroma_db
mkdir chroma_db

# Check permissions
ls -la chroma_db
```

### Performance Optimization

#### Memory Settings
```bash
# Increase Docker memory limit
# Docker Desktop: Settings → Resources → Memory → 8GB+

# Linux: Edit /etc/docker/daemon.json
{
  "default-runtime": "runc",
  "default-shm-size": "2g"
}
```

#### CPU Settings
```bash
# Set worker processes
export MAX_WORKERS=4

# Or in docker-compose.yml
environment:
  - MAX_WORKERS=4
```

## 📦 Production Deployment

### 1. Security Configuration
```bash
# Generate secure API keys
openssl rand -hex 32

# Configure firewall
sudo ufw allow 5002/tcp
sudo ufw allow 5003/tcp
```

### 2. SSL/TLS Setup
```bash
# Using nginx reverse proxy
sudo apt install nginx certbot python3-certbot-nginx

# Configure SSL
sudo certbot --nginx -d your-domain.com
```

### 3. Monitoring Setup
```bash
# Install monitoring tools
pip install prometheus-client grafana-api

# Configure log rotation
sudo logrotate -d /etc/logrotate.d/code-analyzer
```

### 4. Backup Configuration
```bash
# Backup script for ChromaDB
#!/bin/bash
tar -czf "chroma_backup_$(date +%Y%m%d).tar.gz" chroma_db/
```

## ✅ Installation Complete

After successful installation, you should have:

1. ✅ Code Analysis Server running on port 5002
2. ✅ Web Management Interface on port 5003
3. ✅ ChromaDB vector database initialized
4. ✅ OpenWebUI tool installed and configured
5. ✅ Source code directory prepared
6. ✅ All health checks passing

Next steps:
- See [USER_GUIDE.md](USER_GUIDE.md) for usage instructions
- See [DEPLOYMENT.md](DEPLOYMENT.md) for production deployment
- See [TROUBLESHOOTING.md](TROUBLESHOOTING.md) for common issues
