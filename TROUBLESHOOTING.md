# Troubleshooting Guide

Comprehensive troubleshooting guide for the OpenWebUI RAG Code Server, covering common issues, diagnostic procedures, and solutions.

## 🎯 Quick Diagnostic Checklist

Before diving into specific issues, run through this quick checklist:

### 1. Basic Health Checks
```bash
# Check server status
curl http://localhost:5002/health

# Check web management interface
curl http://localhost:5003/api/health

# Check Docker containers
docker ps | grep code-analyzer

# Check logs
docker logs code-analyzer-server
```

### 2. Service Dependencies
```bash
# Check Ollama service
curl http://localhost:11434/api/tags

# Check ChromaDB directory
ls -la chroma_db/

# Check source code directory
ls -la source_code/
```

### 3. Network Connectivity
```bash
# Test internal connectivity
docker exec code-analyzer-server curl http://localhost:5002/health

# Test external connectivity
telnet localhost 5002
```

## 🚨 Common Issues and Solutions

### Installation and Deployment Issues

#### Docker Container Won't Start

**Symptoms:**
- Container exits immediately
- "Port already in use" errors
- Permission denied errors

**Diagnostic Steps:**
```bash
# Check container logs
docker logs code-analyzer-server

# Check port usage
netstat -tulpn | grep :5002

# Check file permissions
ls -la chroma_db/ source_code/
```

**Solutions:**
```bash
# Fix port conflicts
docker-compose down
sudo lsof -ti:5002 | xargs sudo kill -9

# Fix permissions
sudo chown -R $USER:$USER chroma_db/ source_code/
chmod -R 755 chroma_db/ source_code/

# Restart with clean state
docker-compose down -v
docker-compose up --build -d
```

#### Python Dependencies Issues

**Symptoms:**
- ModuleNotFoundError during startup
- Import errors in logs
- Version compatibility issues

**Diagnostic Steps:**
```bash
# Check Python version
python --version

# Check installed packages
pip list | grep -E "(fastapi|chromadb|ollama)"

# Check requirements
pip check
```

**Solutions:**
```bash
# Reinstall dependencies
pip install --force-reinstall -r requirements.txt

# Use virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows
pip install -r requirements.txt

# Update pip
pip install --upgrade pip
```

### Server Connectivity Issues

#### Server Returns 500 Internal Server Error

**Symptoms:**
- HTTP 500 responses
- Server crashes on requests
- Unhandled exceptions in logs

**Diagnostic Steps:**
```bash
# Check detailed logs
docker logs code-analyzer-server --tail 100

# Check server startup
curl -v http://localhost:5002/health

# Check memory usage
docker stats code-analyzer-server
```

**Solutions:**
```bash
# Increase memory allocation
# In docker-compose.yml:
deploy:
  resources:
    limits:
      memory: 4G

# Check and fix configuration
# Verify environment variables
docker exec code-analyzer-server env | grep -E "(OLLAMA|CHROMA)"

# Restart with debug mode
docker-compose down
ENVIRONMENT=development docker-compose up
```

#### Connection Refused Errors

**Symptoms:**
- "Connection refused" when accessing API
- OpenWebUI tool can't connect to server
- Web dashboard unreachable

**Diagnostic Steps:**
```bash
# Check if service is running
docker ps | grep code-analyzer

# Check port binding
docker port code-analyzer-server

# Check firewall
sudo ufw status
```

**Solutions:**
```bash
# Restart services
docker-compose restart

# Check Docker network
docker network ls
docker network inspect openwebui_rag_code_server_default

# Fix firewall rules
sudo ufw allow 5002/tcp
sudo ufw allow 5003/tcp

# Check host binding
# In docker-compose.yml, ensure:
ports:
  - "5002:5002"  # Not "127.0.0.1:5002:5002"
```

### Database and Storage Issues

#### ChromaDB Connection Errors

**Symptoms:**
- "Database connection failed" errors
- Empty search results
- Codebase processing fails

**Diagnostic Steps:**
```bash
# Check ChromaDB directory
ls -la chroma_db/
du -sh chroma_db/

# Check database files
find chroma_db/ -name "*.sqlite3" -ls

# Test database access
python -c "import chromadb; client = chromadb.PersistentClient(path='./chroma_db'); print(client.list_collections())"
```

**Solutions:**
```bash
# Reset database
rm -rf chroma_db/
mkdir chroma_db/
docker-compose restart code-analyzer-server

# Fix permissions
sudo chown -R $USER:$USER chroma_db/
chmod -R 755 chroma_db/

# Check disk space
df -h
```

#### Source Code Directory Issues

**Symptoms:**
- "No codebases found" messages
- Processing fails with file not found
- Empty codebase listings

**Diagnostic Steps:**
```bash
# Check source directory structure
find source_code/ -type f -name "*.py" | head -10
find source_code/ -type f -name "*.cpp" | head -10

# Check directory permissions
ls -la source_code/

# Verify mount in container
docker exec code-analyzer-server ls -la /app/source_code/
```

**Solutions:**
```bash
# Fix directory structure
mkdir -p source_code/my_project
# Copy your code to source_code/my_project/

# Fix permissions
chmod -R 755 source_code/

# Check Docker volume mount
# In docker-compose.yml:
volumes:
  - ./source_code:/app/source_code:ro
```

### AI and Ollama Integration Issues

#### Ollama Service Unreachable

**Symptoms:**
- "AI service unavailable" errors
- Timeout errors during AI queries
- Empty AI responses

**Diagnostic Steps:**
```bash
# Check Ollama service
curl http://localhost:11434/api/tags

# Check Ollama models
curl http://localhost:11434/api/tags | jq '.models[].name'

# Test from container
docker exec code-analyzer-server curl http://ollama:11434/api/tags
```

**Solutions:**
```bash
# Start Ollama service
docker-compose up ollama -d

# Pull required models
docker exec ollama ollama pull llama3:latest

# Check network connectivity
docker network inspect openwebui_rag_code_server_default

# Update configuration
# Set correct OLLAMA_HOST in environment
OLLAMA_HOST=http://ollama:11434
```

#### AI Model Not Found

**Symptoms:**
- "Model not found" errors
- AI queries fail with model errors
- Slow AI responses

**Diagnostic Steps:**
```bash
# List available models
curl http://localhost:11434/api/tags

# Check model size and memory
docker exec ollama ollama list

# Check system resources
free -h
nvidia-smi  # If using GPU
```

**Solutions:**
```bash
# Pull missing models
docker exec ollama ollama pull llama3:latest
docker exec ollama ollama pull nomic-embed-text

# Free up memory
docker system prune
docker exec ollama ollama rm unused_model

# Configure model in environment
OLLAMA_MODEL=llama3:latest
```

### Search and Processing Issues

#### No Search Results

**Symptoms:**
- Search queries return empty results
- "No matches found" for obvious code
- Processed codebases show 0 chunks

**Diagnostic Steps:**
```bash
# Check codebase processing status
curl -X POST http://localhost:5002/tools/get_code_stats \
  -H "Content-Type: application/json" \
  -d '{"codebase_name": "your_project"}'

# Check ChromaDB collections
python -c "
import chromadb
client = chromadb.PersistentClient(path='./chroma_db')
print([c.name for c in client.list_collections()])
"

# Test direct search
curl -X POST http://localhost:5002/tools/search_code \
  -H "Content-Type: application/json" \
  -d '{"query": "function", "codebase_name": "your_project"}'
```

**Solutions:**
```bash
# Reprocess codebase
curl -X POST http://localhost:5002/tools/process_codebase \
  -H "Content-Type: application/json" \
  -d '{"codebase_name": "your_project"}'

# Check for excluded directories
# Ensure your code isn't in excluded dirs like:
# build/, test/, __pycache__, node_modules/

# Verify file extensions
# Check that your files have supported extensions:
# .py, .cpp, .c, .cs, .js, .ts, etc.

# Reset and reprocess
curl -X POST http://localhost:5002/tools/delete_codebase \
  -H "Content-Type: application/json" \
  -d '{"codebase_name": "your_project"}'
```

#### Slow Processing Performance

**Symptoms:**
- Codebase processing takes very long
- Search queries timeout
- High CPU/memory usage

**Diagnostic Steps:**
```bash
# Monitor resource usage
docker stats code-analyzer-server

# Check processing logs
docker logs code-analyzer-server | grep -i "processing"

# Check codebase size
find source_code/your_project -type f | wc -l
du -sh source_code/your_project
```

**Solutions:**
```bash
# Increase resource limits
# In docker-compose.yml:
deploy:
  resources:
    limits:
      memory: 8G
      cpus: '4.0'

# Exclude large directories
curl -X POST http://localhost:5002/tools/process_codebase \
  -H "Content-Type: application/json" \
  -d '{
    "codebase_name": "your_project",
    "exclude_dirs": ["build", "node_modules", "vendor", ".git"]
  }'

# Process in smaller chunks
# Split large codebases into smaller projects
```

### OpenWebUI Tool Issues

#### Tool Not Working in Chat

**Symptoms:**
- Tool commands not recognized
- No response from tool
- "Tool not found" errors

**Diagnostic Steps:**
```bash
# Check tool installation in OpenWebUI
# Go to Workspace → Tools and verify installation

# Check model configuration
# Ensure tool is enabled for the model you're using

# Test tool directly
# Try clicking the tool button instead of typing commands
```

**Solutions:**
```bash
# Reinstall tool
# 1. Remove existing tool from OpenWebUI
# 2. Reinstall from open_webui_code_analyzer_tool.py
# 3. Enable for your model

# Check tool configuration
# Verify CODE_ANALYZER_BASE_URL in tool valves

# Update tool valves
CODE_ANALYZER_BASE_URL = "http://your-server:5002"
ENABLE_DEBUG_MODE = True
```

#### Tool Connection Issues

**Symptoms:**
- "Server may be down" messages
- Connection timeout errors
- Authentication failures

**Diagnostic Steps:**
```bash
# Test server connectivity from OpenWebUI host
curl http://your-server:5002/health

# Check tool configuration
# Verify BASE_URL and API_KEY in tool valves

# Check network routing
ping your-server
telnet your-server 5002
```

**Solutions:**
```bash
# Update tool configuration
# Set correct server URL in tool valves
CODE_ANALYZER_BASE_URL = "http://correct-server:5002"

# Check API authentication
# If server requires API key, set in tool valves
OPENWEBUI_API_KEY = "your_api_key"

# Test network connectivity
# Ensure OpenWebUI can reach the code analyzer server
```

## 🔧 Diagnostic Tools and Commands

### Health Check Script

```bash
#!/bin/bash
# health_check.sh

echo "=== Code Analyzer Health Check ==="

# Check services
echo "1. Checking Docker services..."
docker ps | grep -E "(code-analyzer|ollama)"

# Check API endpoints
echo "2. Checking API endpoints..."
curl -s http://localhost:5002/health | jq '.' || echo "API unreachable"
curl -s http://localhost:5003/api/health | jq '.' || echo "Web interface unreachable"

# Check Ollama
echo "3. Checking Ollama service..."
curl -s http://localhost:11434/api/tags | jq '.models[].name' || echo "Ollama unreachable"

# Check storage
echo "4. Checking storage..."
ls -la chroma_db/ source_code/
df -h | grep -E "(chroma_db|source_code)"

# Check logs for errors
echo "5. Checking recent errors..."
docker logs code-analyzer-server --tail 20 | grep -i error
```

### Performance Monitoring

```bash
#!/bin/bash
# monitor_performance.sh

echo "=== Performance Monitoring ==="

# Resource usage
echo "1. Container resource usage:"
docker stats --no-stream code-analyzer-server

# Memory usage
echo "2. Memory breakdown:"
docker exec code-analyzer-server cat /proc/meminfo | grep -E "(MemTotal|MemAvailable|MemFree)"

# Disk usage
echo "3. Disk usage:"
du -sh chroma_db/ source_code/
df -h

# Network connections
echo "4. Network connections:"
docker exec code-analyzer-server netstat -tulpn | grep -E "(5002|11434)"
```

### Log Analysis

```bash
#!/bin/bash
# analyze_logs.sh

echo "=== Log Analysis ==="

# Recent errors
echo "1. Recent errors:"
docker logs code-analyzer-server --tail 100 | grep -i error

# Performance issues
echo "2. Performance warnings:"
docker logs code-analyzer-server --tail 100 | grep -i -E "(slow|timeout|memory)"

# API requests
echo "3. Recent API requests:"
docker logs code-analyzer-server --tail 50 | grep -E "(POST|GET|PUT|DELETE)"

# Ollama interactions
echo "4. Ollama interactions:"
docker logs code-analyzer-server --tail 50 | grep -i ollama
```

## 🆘 Getting Help

### Information to Collect

When seeking help, please provide:

1. **System Information:**
   - Operating system and version
   - Docker and Docker Compose versions
   - Available memory and CPU

2. **Configuration:**
   - Environment variables
   - Docker Compose configuration
   - Tool valve settings (if using OpenWebUI)

3. **Error Details:**
   - Complete error messages
   - Relevant log excerpts
   - Steps to reproduce the issue

4. **Diagnostic Output:**
   - Health check results
   - Resource usage statistics
   - Network connectivity tests

### Support Channels

1. **Documentation**: Check all documentation files first
2. **GitHub Issues**: Create detailed issue reports
3. **Community Forums**: Engage with other users
4. **Debug Mode**: Enable debug logging for detailed information

### Emergency Recovery

If the system is completely broken:

```bash
# Nuclear option - complete reset
docker-compose down -v
docker system prune -a
rm -rf chroma_db/
mkdir chroma_db/
docker-compose up --build -d
```

This will destroy all processed data but should restore functionality.

Remember to backup your `source_code/` directory and any custom configurations before performing emergency recovery procedures.
