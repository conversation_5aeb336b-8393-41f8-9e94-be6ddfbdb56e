{"data_mtime": 1753142844, "dep_lines": [12, 12, 1, 2, 3, 4, 5, 6, 7, 8, 11, 18, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 10, 10, 10, 5, 10, 10, 10, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["chromadb.utils.embedding_functions", "chromadb.utils", "json", "os", "sys", "typing", "ollama", "asyncio", "time", "datetime", "chromadb", "processing_pipeline", "embedding_config", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "chromadb.api", "chromadb.api.types", "chromadb.config", "io", "json.decoder", "json.encoder", "ollama._client", "ollama._types", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "types", "typing_extensions"], "hash": "96d9a2ddd4ab09a097b96c84416143ecbf1e782b", "id": "vector_db_creator", "ignore_all": false, "interface_hash": "b31ff9c7174d2fda92d6a980a2f929455341a9e5", "mtime": 1753142838, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\vector_db_creator.py", "plugin_data": null, "size": 57401, "suppressed": [], "version_id": "1.15.0"}