{"data_mtime": 1753147017, "dep_lines": [3, 2, 1, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 19, 20, 23, 26, 29, 30, 485, 1825, 3284, 3721, 5176, 5852, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 20, 10, 20, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.middleware.cors", "fastapi.responses", "<PERSON><PERSON><PERSON>", "pydantic", "logging", "os", "sys", "json", "time", "pathlib", "chromadb", "ollama", "typing", "datetime", "vector_db_creator", "language_registry", "framework_integration", "embedding_config", "intent_detection_service", "collections", "re", "<PERSON><PERSON><PERSON>", "asyncio", "traceback", "gpu_infrastructure", "psutil", "u<PERSON><PERSON>", "builtins", "_frozen_importlib", "_typeshed", "abc", "annotated_types", "asyncio.events", "asyncio.protocols", "chromadb.api", "chromadb.config", "configparser", "contextlib", "enum", "fastapi.applications", "fastapi.openapi", "fastapi.openapi.models", "fastapi.param_functions", "fastapi.params", "fastapi.routing", "language_framework", "pydantic._internal", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.main", "pydantic.networks", "pydantic.types", "starlette", "starlette.applications", "starlette.middleware", "starlette.middleware.cors", "starlette.requests", "starlette.responses", "starlette.routing", "types", "typing_extensions", "uvicorn._types"], "hash": "5fbb4af67bcf6990e92d0be226fcf784580b55fd", "id": "main", "ignore_all": true, "interface_hash": "2e11ddee139f8b7967d0631295178b18e61b4106", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\main.py", "plugin_data": null, "size": 271104, "suppressed": [], "version_id": "1.15.0"}